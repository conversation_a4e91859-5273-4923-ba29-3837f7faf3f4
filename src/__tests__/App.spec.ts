import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import App from '../App.vue'
import Home from '../pages/Home.vue'

// Create a mock router for testing
const router = createRouter({
  history: createWebHistory(),
  routes: [{ path: '/', component: Home }],
})

describe('App', () => {
  it('mounts renders properly', async () => {
    const wrapper = mount(App, {
      global: {
        plugins: [router],
      },
    })

    // Wait for router to be ready
    await router.isReady()

    // Check that the app contains navigation and home content
    expect(wrapper.text()).toContain('首页')
    expect(wrapper.text()).toContain('多媒体内容平台')
  })
})
