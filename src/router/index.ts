import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/pages/Home.vue'
import Category from '@/pages/Category.vue'
import Detail from '@/pages/Detail.vue'
import Reader from '@/pages/Reader.vue'
import Player from '@/pages/Player.vue'
import Search from '@/pages/Search.vue'
import UserProfile from '@/pages/UserProfile.vue'
import CreatorDashboard from '@/pages/CreatorDashboard.vue'
import AuthLogin from '@/pages/AuthLogin.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home,
    },
    {
      path: '/category/:type',
      name: 'category',
      component: Category,
    },
    {
      path: '/detail/:id',
      name: 'detail',
      component: Detail,
    },
    {
      path: '/reader/:id',
      name: 'reader',
      component: Reader,
    },
    {
      path: '/player/:id',
      name: 'player',
      component: Player,
    },
    {
      path: '/search',
      name: 'search',
      component: Search,
    },
    {
      path: '/user/profile',
      name: 'userProfile',
      component: UserProfile,
    },
    {
      path: '/creator/dashboard',
      name: 'creatorDashboard',
      component: CreatorDashboard,
    },
    {
      path: '/auth/login',
      name: 'authLogin',
      component: AuthLogin,
    },
  ],
})

export default router
