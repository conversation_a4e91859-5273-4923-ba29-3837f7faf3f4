<script setup lang="ts"></script>

<template>
  <nav style="display:flex; gap:12px; padding:8px 0; border-bottom:1px solid #eee;">
    <RouterLink to="/">首页</RouterLink>
    <RouterLink to="/category/comic">漫画分类</RouterLink>
    <RouterLink to="/player/1">播放器示例</RouterLink>
    <RouterLink to="/search?q=vue">搜索“vue”</RouterLink>
    <RouterLink to="/user/profile">我的</RouterLink>
    <RouterLink to="/creator/dashboard">创作者中心</RouterLink>
    <RouterLink to="/auth/login">登录</RouterLink>
  </nav>
</template>

<style scoped>
nav a {
  text-decoration: none;
}
nav a.router-link-active {
  font-weight: 600;
}
</style>

